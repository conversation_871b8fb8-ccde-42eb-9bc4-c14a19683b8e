import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import type { User } from '@supabase/supabase-js';

/**
 * Obtiene el usuario autenticado en el servidor
 * Redirige a login si no está autenticado
 */
export async function getAuthenticatedUser(): Promise<User> {
  const supabase = await createClient();
  
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    redirect('/auth/login');
  }
  
  return user;
}

/**
 * Obtiene el usuario autenticado sin redirección
 * Retorna null si no está autenticado
 */
export async function getCurrentUser(): Promise<User | null> {
  const supabase = await createClient();
  
  const { data: { user } } = await supabase.auth.getUser();
  
  return user;
}

/**
 * Verifica si el usuario está autenticado
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getCurrentUser();
  return !!user;
}

/**
 * Obtiene el perfil del usuario autenticado
 */
export async function getUserProfile(userId?: string) {
  try {
    const { createClient: createClientWithoutTypes } = await import('@/lib/supabase/client');
    const supabase = createClientWithoutTypes();
    
    const userIdToUse = userId || (await getAuthenticatedUser()).id;
    
    const { data, error } = await (supabase as unknown as any)
      .from('user_profiles')
      .select('*')
      .eq('id', userIdToUse)
      .single();
    
    if (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in getUserProfile:', error);
    return null;
  }
}

/**
 * Crea un perfil de usuario después del registro
 */
export async function createUserProfile(userId: string, email: string, fullName: string) {
  try {
    const { createClient: createClientWithoutTypes } = await import('@/lib/supabase/client');
    const supabase = createClientWithoutTypes();
    
    const { data, error } = await (supabase as unknown as any)
      .from('user_profiles')
      .insert({
        id: userId,
        email,
        full_name: fullName,
        currency: 'PYG',
        timezone: 'America/Asuncion',
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error in createUserProfile:', error);
    throw error;
  }
}

/**
 * Actualiza el perfil del usuario
 */
export async function updateUserProfile(userId: string, updates: Record<string, unknown>) {
  try {
    const { createClient: createClientWithoutTypes } = await import('@/lib/supabase/client');
    const supabase = createClientWithoutTypes();
    
    const { data, error } = await (supabase as unknown as any)
      .from('user_profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error in updateUserProfile:', error);
    throw error;
  }
}

/**
 * Verifica si el usuario tiene permisos para acceder a un recurso
 */
export async function hasPermission(resourceUserId: string): Promise<boolean> {
  const currentUser = await getCurrentUser();
  
  if (!currentUser) {
    return false;
  }
  
  // El usuario solo puede acceder a sus propios recursos
  return currentUser.id === resourceUserId;
}

/**
 * Middleware helper para verificar autenticación en API routes
 */
export async function requireAuth() {
  const user = await getCurrentUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}

/**
 * Obtiene la sesión actual
 */
export async function getSession() {
  const supabase = await createClient();
  
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error) {
    console.error('Error getting session:', error);
    return null;
  }
  
  return session;
}
