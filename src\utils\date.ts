import { 
  format, 
  parseISO, 
  startOfMonth, 
  endOfMonth, 
  startOfYear, 
  endOfYear,
  addMonths,
  subMonths,
  addDays,
  subDays,
  isAfter,
  isBefore,
  isSameDay,
  differenceInDays,
  differenceInMonths,
  differenceInYears
} from 'date-fns';
import { es } from 'date-fns/locale';
import { CURRENCY_CONFIG } from '@/lib/constants';

/**
 * Formatea una fecha usando date-fns con localización en español
 */
export function formatDate(date: string | Date, formatStr: string = 'dd/MM/yyyy'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  
  if (isNaN(dateObj.getTime())) return '';
  
  return format(dateObj, formatStr, { locale: es });
}

/**
 * Obtiene el primer día del mes actual
 */
export function getStartOfCurrentMonth(): Date {
  return startOfMonth(new Date());
}

/**
 * Obtiene el último día del mes actual
 */
export function getEndOfCurrentMonth(): Date {
  return endOfMonth(new Date());
}

/**
 * Obtiene el primer día del año actual
 */
export function getStartOfCurrentYear(): Date {
  return startOfYear(new Date());
}

/**
 * Obtiene el último día del año actual
 */
export function getEndOfCurrentYear(): Date {
  return endOfYear(new Date());
}

/**
 * Obtiene el rango de fechas del mes anterior
 */
export function getPreviousMonthRange(): { start: Date; end: Date } {
  const now = new Date();
  const previousMonth = subMonths(now, 1);
  
  return {
    start: startOfMonth(previousMonth),
    end: endOfMonth(previousMonth),
  };
}

/**
 * Obtiene el rango de fechas del próximo mes
 */
export function getNextMonthRange(): { start: Date; end: Date } {
  const now = new Date();
  const nextMonth = addMonths(now, 1);
  
  return {
    start: startOfMonth(nextMonth),
    end: endOfMonth(nextMonth),
  };
}

/**
 * Obtiene los últimos N días
 */
export function getLastNDays(days: number): { start: Date; end: Date } {
  const now = new Date();
  
  return {
    start: subDays(now, days - 1),
    end: now,
  };
}

/**
 * Obtiene los próximos N días
 */
export function getNextNDays(days: number): { start: Date; end: Date } {
  const now = new Date();
  
  return {
    start: now,
    end: addDays(now, days - 1),
  };
}

/**
 * Verifica si una fecha está en el rango especificado
 */
export function isDateInRange(date: Date, start: Date, end: Date): boolean {
  return (isAfter(date, start) || isSameDay(date, start)) && 
         (isBefore(date, end) || isSameDay(date, end));
}

/**
 * Calcula los días restantes hasta una fecha
 */
export function getDaysUntil(targetDate: string | Date): number {
  const target = typeof targetDate === 'string' ? parseISO(targetDate) : targetDate;
  const now = new Date();
  
  return differenceInDays(target, now);
}

/**
 * Calcula los meses restantes hasta una fecha
 */
export function getMonthsUntil(targetDate: string | Date): number {
  const target = typeof targetDate === 'string' ? parseISO(targetDate) : targetDate;
  const now = new Date();
  
  return differenceInMonths(target, now);
}

/**
 * Calcula los años restantes hasta una fecha
 */
export function getYearsUntil(targetDate: string | Date): number {
  const target = typeof targetDate === 'string' ? parseISO(targetDate) : targetDate;
  const now = new Date();
  
  return differenceInYears(target, now);
}

/**
 * Convierte una fecha a formato ISO para la base de datos
 */
export function toISODate(date: Date): string {
  return format(date, 'yyyy-MM-dd');
}

/**
 * Convierte una fecha a formato ISO con tiempo para la base de datos
 */
export function toISODateTime(date: Date): string {
  return date.toISOString();
}

/**
 * Obtiene el nombre del mes en español
 */
export function getMonthName(date: Date): string {
  return format(date, 'MMMM', { locale: es });
}

/**
 * Obtiene el nombre del día de la semana en español
 */
export function getDayName(date: Date): string {
  return format(date, 'EEEE', { locale: es });
}

/**
 * Verifica si una fecha es hoy
 */
export function isToday(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return isSameDay(dateObj, new Date());
}

/**
 * Verifica si una fecha es en el futuro
 */
export function isFuture(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return isAfter(dateObj, new Date());
}

/**
 * Verifica si una fecha es en el pasado
 */
export function isPast(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return isBefore(dateObj, new Date());
}

/**
 * Obtiene el rango de fechas para un período específico
 */
export function getDateRangeForPeriod(period: 'week' | 'month' | 'year', date?: Date): { start: Date; end: Date } {
  const baseDate = date || new Date();
  
  switch (period) {
    case 'week':
      const startOfWeek = subDays(baseDate, baseDate.getDay());
      return {
        start: startOfWeek,
        end: addDays(startOfWeek, 6),
      };
    
    case 'month':
      return {
        start: startOfMonth(baseDate),
        end: endOfMonth(baseDate),
      };
    
    case 'year':
      return {
        start: startOfYear(baseDate),
        end: endOfYear(baseDate),
      };
    
    default:
      throw new Error(`Período no soportado: ${period}`);
  }
}

/**
 * Formatea un rango de fechas para mostrar
 */
export function formatDateRange(start: Date, end: Date): string {
  if (isSameDay(start, end)) {
    return formatDate(start);
  }
  
  const startFormatted = formatDate(start);
  const endFormatted = formatDate(end);
  
  return `${startFormatted} - ${endFormatted}`;
}

/**
 * Obtiene la fecha actual en la zona horaria configurada
 */
export function getCurrentDateInTimezone(): Date {
  return new Date();
}

/**
 * Convierte una fecha a la zona horaria local
 */
export function toLocalTimezone(date: string | Date): Date {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return new Date(dateObj.toLocaleString('en-US', { timeZone: CURRENCY_CONFIG.timezone }));
}
