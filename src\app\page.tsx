'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import {
  Box,
  Container,
  Paper,
  Button,
  Typography,
  CircularProgress,
  AppBar,
  Toolbar,
} from '@mui/material';
import { 
  TrendingUp, 
  Security, 
  Smartphone, 
  BarChart,
  CurrencyExchange,
} from '@mui/icons-material';

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (user) {
    return null; // Will redirect to dashboard
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Header */}
      <AppBar 
        position="sticky" 
        elevation={1}
        sx={{ 
          bgcolor: 'rgba(255, 255, 255, 0.9)', 
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: 'primary.main',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <CurrencyExchange sx={{ color: 'white', fontSize: 20 }} />
              </Box>
              <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
                Expense Tracker PY
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Link href="/auth/login" passHref>
              <Button variant="text" sx={{ color: 'text.secondary' }}>
                Iniciar Sesión
              </Button>
            </Link>
            <Link href="/auth/register" passHref>
              <Button variant="contained">
                Registrarse
              </Button>
            </Link>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Hero Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Box sx={{ textAlign: 'center', mb: 8 }}>
          <Typography 
            variant="h2" 
            component="h1" 
            fontWeight="bold" 
            sx={{ 
              mb: 3,
              background: 'linear-gradient(45deg, #3b82f6, #1d4ed8)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              color: 'transparent',
            }}
          >
            Gestiona tus Finanzas en Guaraníes
          </Typography>
          <Typography 
            variant="h5" 
            color="text.secondary" 
            sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}
          >
            La aplicación más completa para controlar tus gastos, ingresos y presupuestos en Paraguay
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link href="/auth/register" passHref>
              <Button 
                variant="contained" 
                size="large"
                sx={{ 
                  px: 4, 
                  py: 1.5, 
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  boxShadow: 3,
                  '&:hover': { boxShadow: 6 }
                }}
              >
                Comenzar Gratis
              </Button>
            </Link>
            <Link href="/auth/login" passHref>
              <Button 
                variant="outlined" 
                size="large"
                sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}
              >
                Ya tengo cuenta
              </Button>
            </Link>
          </Box>
        </Box>

        {/* Features */}
        <Box sx={{ mb: 8 }}>
          <Typography variant="h4" textAlign="center" fontWeight="bold" sx={{ mb: 2 }}>
            ¿Por qué elegir Expense Tracker PY?
          </Typography>
          <Typography variant="body1" textAlign="center" color="text.secondary" sx={{ mb: 6 }}>
            Diseñado específicamente para el mercado paraguayo
          </Typography>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
            <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
              <Paper 
                elevation={2} 
                sx={{ 
                  p: 3, 
                  textAlign: 'center', 
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': { 
                    transform: 'translateY(-4px)',
                    boxShadow: 4 
                  }
                }}
              >
                <Box sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'primary.main',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2
                }}>
                  <TrendingUp sx={{ fontSize: 24, color: 'white' }} />
                </Box>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Control Total
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Monitorea todos tus gastos e ingresos en tiempo real
                </Typography>
              </Paper>
            </Box>

            <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
              <Paper 
                elevation={2} 
                sx={{ 
                  p: 3, 
                  textAlign: 'center', 
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': { 
                    transform: 'translateY(-4px)',
                    boxShadow: 4 
                  }
                }}
              >
                <Box sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'success.main',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2
                }}>
                  <Security sx={{ fontSize: 24, color: 'white' }} />
                </Box>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  100% Seguro
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Tus datos están protegidos con encriptación de nivel bancario
                </Typography>
              </Paper>
            </Box>

            <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}
              >
                <Box sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'warning.main',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2
                }}>
                  <Smartphone sx={{ fontSize: 24, color: 'white' }} />
                </Box>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Móvil First
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Diseñado para funcionar perfectamente en tu celular
                </Typography>
              </Paper>
            </Box>

            <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}
              >
                <Box sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'info.main',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2
                }}>
                  <BarChart sx={{ fontSize: 24, color: 'white' }} />
                </Box>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Reportes Inteligentes
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Gráficos y análisis que te ayudan a tomar mejores decisiones
                </Typography>
              </Paper>
            </Box>
          </Box>
        </Box>

        {/* CTA Section */}
        <Paper 
          elevation={3}
          sx={{ 
            p: 6, 
            textAlign: 'center', 
            borderRadius: 3,
            background: 'linear-gradient(135deg, #f9fafb 0%, #ffffff 50%, #eff6ff 100%)',
          }}
        >
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            ¿Listo para tomar control de tus finanzas?
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Únete a miles de paraguayos que ya están mejorando su salud financiera
          </Typography>
          <Link href="/auth/register" passHref>
            <Button 
              variant="contained" 
              size="large"
              sx={{ 
                px: 6, 
                py: 2, 
                fontSize: '1.2rem',
                fontWeight: 600,
                boxShadow: 3,
                '&:hover': { boxShadow: 6 }
              }}
            >
              Crear Cuenta Gratis
            </Button>
          </Link>
        </Paper>
      </Container>
    </Box>
  );
}
