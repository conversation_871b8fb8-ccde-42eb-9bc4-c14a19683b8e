import type { Metada<PERSON> } from "next";
import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { theme } from '@/lib/theme';
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Expense Tracker PY",
  description: "Gestor de gastos personal en guaraníes paraguayos",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const currentYear = new Date().getFullYear();

  return (
    <html lang="es">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <div style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
            <main style={{ flex: 1 }}>
              {children}
            </main>
            <footer style={{
              padding: '1rem',
              textAlign: 'center',
              borderTop: '1px solid #e5e5e5',
              backgroundColor: '#f9fafb',
              color: '#6b7280',
              fontSize: '0.875rem'
            }}>
              © {currentYear} Expense Tracker PY. Todos los derechos reservados.
            </footer>
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
