'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { forgotPasswordSchema, type ForgotPasswordFormData } from '@/utils/validation';
import { useAuth } from '@/hooks/useAuth';
import { translateAuthError } from '@/utils/errorMessages';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  ArrowBack,
  Email,
  CurrencyExchange,
} from '@mui/icons-material';

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const { resetPassword } = useAuth();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const { error } = await resetPassword(data.email);
      
      if (error) {
        setError(translateAuthError(error.message));
      } else {
        setSuccess('Te hemos enviado un email con las instrucciones para restablecer tu contraseña.');
      }
    } catch {
      setError('Error inesperado. Intenta nuevamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #f9fafb 0%, #ffffff 50%, #eff6ff 100%)',
        py: 6,
        px: 2,
      }}
    >
      <Container maxWidth="sm">
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 48,
                height: 48,
                bgcolor: 'primary.main',
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
              }}
            >
              <CurrencyExchange sx={{ color: 'white', fontSize: 28 }} />
            </Box>
            <Typography variant="h4" component="h1" fontWeight="bold" color="text.primary">
              Expense Tracker PY
            </Typography>
          </Box>
          <Typography variant="body1" color="text.secondary">
            Recupera el acceso a tu cuenta
          </Typography>
        </Box>

        <Paper
          elevation={3}
          sx={{
            p: 4,
            borderRadius: 3,
            bgcolor: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
          }}
        >
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 1 }}>
              <Email color="primary" />
              <Typography variant="h5" component="h2" fontWeight="600">
                Recuperar Contraseña
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Ingresa tu email y te enviaremos las instrucciones para restablecer tu contraseña
            </Typography>
          </Box>

          <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                {success}
              </Alert>
            )}

            <TextField
              fullWidth
              id="email"
              label="Email"
              type="email"
              placeholder="<EMAIL>"
              {...register('email')}
              error={!!errors.email}
              helperText={errors.email?.message}
              sx={{ mb: 3 }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading || success !== ''}
              sx={{
                py: 1.5,
                fontWeight: 600,
                boxShadow: 2,
                '&:hover': {
                  boxShadow: 4,
                },
                mb: 2,
              }}
            >
              {isLoading ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                  Enviando...
                </>
              ) : success ? (
                'Email Enviado'
              ) : (
                'Enviar Instrucciones'
              )}
            </Button>

            <Box sx={{ textAlign: 'center' }}>
              <Link href="/auth/login" passHref>
                <Typography
                  component="a"
                  variant="body2"
                  sx={{
                    color: 'primary.main',
                    textDecoration: 'none',
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: 0.5,
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  <ArrowBack fontSize="small" />
                  Volver al inicio de sesión
                </Typography>
              </Link>
            </Box>
          </Box>
        </Paper>

        {success && (
          <Paper
            elevation={2}
            sx={{
              mt: 3,
              p: 3,
              bgcolor: 'primary.50',
              border: '1px solid',
              borderColor: 'primary.200',
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <Email sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" fontWeight="600" color="primary.dark" gutterBottom>
                Revisa tu email
              </Typography>
              <Typography variant="body2" color="primary.dark" paragraph>
                Si el email existe en nuestro sistema, recibirás las instrucciones en unos minutos.
              </Typography>
              <Typography variant="caption" color="primary.main">
                No olvides revisar tu carpeta de spam.
              </Typography>
            </Box>
          </Paper>
        )}
      </Container>
    </Box>
  );
}
