/**
 * Sistema de Colores Profesional para Expense Tracker PY
 * Diseño minimalista y formalista con paleta coherente
 */

// Colores Primarios - Azul profesional y confiable
export const PRIMARY_COLORS = {
  50: '#f0f9ff',   // Azul muy claro para fondos
  100: '#e0f2fe',  // Azul claro para hover states
  200: '#bae6fd',  // Azul suave para borders
  300: '#7dd3fc',  // Azul medio para elementos secundarios
  400: '#38bdf8',  // Azul vibrante para accents
  500: '#0ea5e9',  // Azul principal - color primario
  600: '#0284c7',  // Azul oscuro para hover
  700: '#0369a1',  // Azul más oscuro para texto
  800: '#075985',  // Azul muy oscuro
  900: '#0c4a6e',  // Azul profundo
} as const;

// Colores Neutros - Grises profesionales
export const NEUTRAL_COLORS = {
  50: '#fafafa',   // Blanco casi puro para fondos
  100: '#f5f5f5',  // Gris muy claro para cards
  200: '#e5e5e5',  // Gris claro para borders
  300: '#d4d4d4',  // Gris medio para dividers
  400: '#a3a3a3',  // Gris para texto secundario
  500: '#737373',  // Gris para texto normal
  600: '#525252',  // Gris oscuro para texto importante
  700: '#404040',  // Gris muy oscuro para headings
  800: '#262626',  // Casi negro para texto principal
  900: '#171717',  // Negro para texto crítico
} as const;

// Colores de Estado - Para feedback y alertas
export const STATUS_COLORS = {
  success: {
    50: '#f0fdf4',   // Verde muy claro
    100: '#dcfce7',  // Verde claro
    500: '#22c55e',  // Verde principal
    600: '#16a34a',  // Verde oscuro
    700: '#15803d',  // Verde muy oscuro
  },
  warning: {
    50: '#fffbeb',   // Amarillo muy claro
    100: '#fef3c7',  // Amarillo claro
    500: '#f59e0b',  // Amarillo principal
    600: '#d97706',  // Amarillo oscuro
    700: '#b45309',  // Amarillo muy oscuro
  },
  error: {
    50: '#fef2f2',   // Rojo muy claro
    100: '#fee2e2',  // Rojo claro
    500: '#ef4444',  // Rojo principal
    600: '#dc2626',  // Rojo oscuro
    700: '#b91c1c',  // Rojo muy oscuro
  },
  info: {
    50: '#f0f9ff',   // Azul info muy claro
    100: '#e0f2fe',  // Azul info claro
    500: '#3b82f6',  // Azul info principal
    600: '#2563eb',  // Azul info oscuro
    700: '#1d4ed8',  // Azul info muy oscuro
  },
} as const;

// Colores para Categorías de Gastos - Paleta coherente y profesional
export const CATEGORY_COLORS = {
  // Gastos esenciales - Tonos azules y grises
  food: '#0ea5e9',        // Azul principal
  transport: '#0284c7',   // Azul oscuro
  utilities: '#075985',   // Azul muy oscuro
  health: '#22c55e',      // Verde (salud = positivo)
  
  // Gastos personales - Tonos cálidos pero profesionales
  entertainment: '#8b5cf6', // Púrpura suave
  shopping: '#f59e0b',      // Amarillo/naranja
  education: '#3b82f6',     // Azul info
  clothing: '#ec4899',      // Rosa profesional
  
  // Gastos del hogar - Tonos neutros
  home: '#737373',          // Gris medio
  maintenance: '#525252',   // Gris oscuro
  
  // Ingresos - Tonos verdes
  salary: '#16a34a',        // Verde principal
  freelance: '#15803d',     // Verde oscuro
  investment: '#059669',    // Verde esmeralda
  other_income: '#10b981',  // Verde claro
  
  // Otros - Tonos neutros
  other: '#6b7280',         // Gris neutro
  transfer: '#9ca3af',      // Gris claro
} as const;

// Gradientes profesionales
export const GRADIENTS = {
  primary: 'from-blue-50 to-blue-100',
  success: 'from-green-50 to-green-100',
  warning: 'from-yellow-50 to-yellow-100',
  error: 'from-red-50 to-red-100',
  neutral: 'from-gray-50 to-gray-100',
  hero: 'from-blue-50 via-white to-blue-50',
  card: 'from-white to-gray-50',
} as const;

// Sombras profesionales
export const SHADOWS = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  card: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  button: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
} as const;

// Configuración de tema
export const THEME_CONFIG = {
  // Colores principales del tema
  colors: {
    primary: PRIMARY_COLORS[500],
    primaryHover: PRIMARY_COLORS[600],
    primaryLight: PRIMARY_COLORS[100],
    
    background: '#ffffff',
    backgroundSecondary: NEUTRAL_COLORS[50],
    backgroundTertiary: NEUTRAL_COLORS[100],
    
    text: NEUTRAL_COLORS[800],
    textSecondary: NEUTRAL_COLORS[600],
    textMuted: NEUTRAL_COLORS[500],
    
    border: NEUTRAL_COLORS[200],
    borderHover: NEUTRAL_COLORS[300],
    
    success: STATUS_COLORS.success[500],
    warning: STATUS_COLORS.warning[500],
    error: STATUS_COLORS.error[500],
    info: STATUS_COLORS.info[500],
  },
  
  // Espaciado consistente
  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    '2xl': '3rem',   // 48px
    '3xl': '4rem',   // 64px
  },
  
  // Bordes redondeados
  borderRadius: {
    sm: '0.25rem',   // 4px
    md: '0.375rem',  // 6px
    lg: '0.5rem',    // 8px
    xl: '0.75rem',   // 12px
    '2xl': '1rem',   // 16px
    full: '9999px',
  },
  
  // Tipografía
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace'],
    },
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
} as const;

// Utilidades para obtener colores
export const getStatusColor = (status: 'success' | 'warning' | 'error' | 'info', shade: keyof typeof STATUS_COLORS.success = 500) => {
  return STATUS_COLORS[status][shade];
};

export const getCategoryColor = (category: keyof typeof CATEGORY_COLORS) => {
  return CATEGORY_COLORS[category];
};

export const getPrimaryColor = (shade: keyof typeof PRIMARY_COLORS = 500) => {
  return PRIMARY_COLORS[shade];
};

export const getNeutralColor = (shade: keyof typeof NEUTRAL_COLORS = 500) => {
  return NEUTRAL_COLORS[shade];
};

// Clases CSS predefinidas para uso común
export const COLOR_CLASSES = {
  // Backgrounds
  bgPrimary: 'bg-blue-500',
  bgPrimaryLight: 'bg-blue-50',
  bgSecondary: 'bg-gray-50',
  bgWhite: 'bg-white',
  
  // Text colors
  textPrimary: 'text-gray-800',
  textSecondary: 'text-gray-600',
  textMuted: 'text-gray-500',
  textSuccess: 'text-green-600',
  textWarning: 'text-yellow-600',
  textError: 'text-red-600',
  
  // Borders
  borderDefault: 'border-gray-200',
  borderHover: 'border-gray-300',
  borderPrimary: 'border-blue-500',
  
  // Hover states
  hoverPrimary: 'hover:bg-blue-600',
  hoverSecondary: 'hover:bg-gray-100',
  hoverSuccess: 'hover:bg-green-600',
  hoverWarning: 'hover:bg-yellow-600',
  hoverError: 'hover:bg-red-600',
} as const;
