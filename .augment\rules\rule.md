---
type: "always_apply"
---

-High-Level Overview of Optimization Techniques
-Remove unnecessary use client directives – Use React Server Components by default
-Optimize images with Next.js Image component – Replace standard <img> tags
-Reduce bundle size by avoiding heavy libraries – Use native JavaScript APIs instead
-Implement streaming with Suspense – Avoid blocking the entire page while data loads
-Fetch user sessions on the client side in layouts – Preserve static rendering
-Use middleware for authentication – Instead of checking auth in layouts
-Cache repeated function calls – Use React's cache() for duplicate data fetches
-Apply <PERSON>'s Law to UI structure – Avoid overloading users by grouping information into ~7 meaningful chunks
-Use Skeletons instead of loading spinners – Preload layout with placeholders to reduce cognitive load and improve perceived performance