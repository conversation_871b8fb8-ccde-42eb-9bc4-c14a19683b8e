import { CURRENCY_CONFIG, DATE_FORMATS } from '@/lib/constants';

/**
 * Formatea un monto en guaraníes con el símbolo de moneda
 */
export function formatCurrency(amount: number): string {
  if (isNaN(amount)) return `${CURRENCY_CONFIG.symbol} 0`;
  
  return new Intl.NumberFormat(CURRENCY_CONFIG.locale, {
    style: 'currency',
    currency: CURRENCY_CONFIG.code,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Formatea un número sin símbolo de moneda
 */
export function formatNumber(number: number): string {
  if (isNaN(number)) return '0';
  
  return new Intl.NumberFormat(CURRENCY_CONFIG.locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(number);
}

/**
 * Formatea una fecha según el formato especificado
 */
export function formatDate(date: string | Date, format: keyof typeof DATE_FORMATS = 'SHORT'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) return '';

  // Mapeo básico de formatos para Intl.DateTimeFormat
  const options: Intl.DateTimeFormatOptions = {};
  
  switch (format) {
    case 'SHORT':
      options.day = '2-digit';
      options.month = '2-digit';
      options.year = 'numeric';
      break;
    case 'LONG':
      options.day = 'numeric';
      options.month = 'long';
      options.year = 'numeric';
      break;
    case 'WITH_TIME':
      options.day = '2-digit';
      options.month = '2-digit';
      options.year = 'numeric';
      options.hour = '2-digit';
      options.minute = '2-digit';
      break;
    case 'ISO':
      return dateObj.toISOString().split('T')[0];
    default:
      options.day = '2-digit';
      options.month = '2-digit';
      options.year = 'numeric';
  }
  
  return new Intl.DateTimeFormat(CURRENCY_CONFIG.locale, options).format(dateObj);
}

/**
 * Formatea un porcentaje
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  if (isNaN(value)) return '0%';
  
  return new Intl.NumberFormat(CURRENCY_CONFIG.locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);
}

/**
 * Convierte un string a número, removiendo caracteres no numéricos
 */
export function parseNumber(value: string): number {
  const cleaned = value.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Formatea un nombre para mostrar (capitaliza primera letra)
 */
export function formatName(name: string): string {
  if (!name) return '';
  
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Formatea un número de teléfono
 */
export function formatPhone(phone: string): string {
  if (!phone) return '';
  
  // Remover caracteres no numéricos excepto +
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // Si empieza con +595 (Paraguay)
  if (cleaned.startsWith('+595')) {
    const number = cleaned.slice(4);
    if (number.length === 9) {
      return `+595 ${number.slice(0, 3)} ${number.slice(3, 6)} ${number.slice(6)}`;
    }
  }
  
  // Formato local paraguayo (9 dígitos)
  if (cleaned.length === 9) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }
  
  return phone;
}

/**
 * Trunca un texto a una longitud específica
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  
  return text.slice(0, maxLength - 3) + '...';
}

/**
 * Formatea el tiempo relativo (hace X días, etc.)
 */
export function formatRelativeTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Hace un momento';
  if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} minutos`;
  if (diffInSeconds < 86400) return `Hace ${Math.floor(diffInSeconds / 3600)} horas`;
  if (diffInSeconds < 2592000) return `Hace ${Math.floor(diffInSeconds / 86400)} días`;
  if (diffInSeconds < 31536000) return `Hace ${Math.floor(diffInSeconds / 2592000)} meses`;
  
  return `Hace ${Math.floor(diffInSeconds / 31536000)} años`;
}

/**
 * Formatea un rango de fechas
 */
export function formatDateRange(startDate: Date, endDate: Date): string {
  const start = formatDate(startDate);
  const end = formatDate(endDate);
  
  if (start === end) return start;
  
  return `${start} - ${end}`;
}

/**
 * Obtiene las iniciales de un nombre
 */
export function getInitials(name: string): string {
  if (!name) return '';
  
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
}

/**
 * Formatea un número de tarjeta de crédito (muestra solo los últimos 4 dígitos)
 */
export function formatCardNumber(lastFourDigits: string): string {
  if (!lastFourDigits || lastFourDigits.length !== 4) return '';
  
  return `**** **** **** ${lastFourDigits}`;
}

/**
 * Calcula y formatea el progreso como porcentaje
 */
export function formatProgress(current: number, target: number): string {
  if (target === 0) return '0%';
  
  const percentage = Math.min((current / target) * 100, 100);
  return formatPercentage(percentage, 0);
}

/**
 * Formatea un monto con signo positivo/negativo
 */
export function formatAmountWithSign(amount: number, type: 'income' | 'expense'): string {
  const formattedAmount = formatCurrency(Math.abs(amount));
  
  if (type === 'income') {
    return `+${formattedAmount}`;
  } else {
    return `-${formattedAmount}`;
  }
}

/**
 * Convierte bytes a formato legible
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
