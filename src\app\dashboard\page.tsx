'use client';

import { useAuth } from '@/hooks/useAuth';
import { formatCurrency } from '@/utils/format';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Paper,
  Button,
  Typography,
  AppBar,
  Toolbar,
  CircularProgress,
} from '@mui/material';
import {
  Logout,
  Add,
  CreditCard,
  TrackChanges,
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';

export default function DashboardPage() {
  const { user, profile, signOut, loading } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut();
    router.push('/auth/login');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Header */}
      <AppBar 
        position="sticky" 
        elevation={1}
        sx={{ 
          bgcolor: 'rgba(255, 255, 255, 0.9)', 
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 2 }}>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: 'primary.main',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold' }}>
                  ₲
                </Typography>
              </Box>
              <Box>
                <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
                  Dashboard
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  Bienvenido, {profile?.full_name || user.email?.split('@')[0]}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Button
            variant="outlined"
            startIcon={<Logout />}
            onClick={handleSignOut}
            sx={{ color: 'text.secondary' }}
          >
            Cerrar Sesión
          </Button>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Stats Cards */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4 }}>
          {/* Ingresos del Mes */}
          <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
            <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary" fontWeight={500}>
                  Ingresos del Mes
                </Typography>
                <Box sx={{ 
                  width: 32, 
                  height: 32, 
                  bgcolor: 'success.light', 
                  borderRadius: 1, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center' 
                }}>
                  <TrendingUp sx={{ fontSize: 16, color: 'success.dark' }} />
                </Box>
              </Box>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {formatCurrency(0)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                +0% desde el mes pasado
              </Typography>
            </Paper>
          </Box>

          {/* Gastos del Mes */}
          <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
            <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary" fontWeight={500}>
                  Gastos del Mes
                </Typography>
                <Box sx={{ 
                  width: 32, 
                  height: 32, 
                  bgcolor: 'error.light', 
                  borderRadius: 1, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center' 
                }}>
                  <TrendingDown sx={{ fontSize: 16, color: 'error.dark' }} />
                </Box>
              </Box>
              <Typography variant="h4" fontWeight="bold" color="error.main">
                {formatCurrency(0)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                +0% desde el mes pasado
              </Typography>
            </Paper>
          </Box>

          {/* Balance */}
          <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
            <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary" fontWeight={500}>
                  Balance
                </Typography>
                <Box sx={{ 
                  width: 32, 
                  height: 32, 
                  bgcolor: 'primary.light', 
                  borderRadius: 1, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center' 
                }}>
                  <TrackChanges sx={{ fontSize: 16, color: 'primary.dark' }} />
                </Box>
              </Box>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                {formatCurrency(0)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Balance actual
              </Typography>
            </Paper>
          </Box>

          {/* Tarjetas de Crédito */}
          <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
            <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary" fontWeight={500}>
                  Tarjetas de Crédito
                </Typography>
                <Box sx={{ 
                  width: 32, 
                  height: 32, 
                  bgcolor: 'warning.light', 
                  borderRadius: 1, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center' 
                }}>
                  <CreditCard sx={{ fontSize: 16, color: 'warning.dark' }} />
                </Box>
              </Box>
              <Typography variant="h4" fontWeight="bold" color="warning.main">
                0
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Tarjetas registradas
              </Typography>
            </Paper>
          </Box>
        </Box>

        {/* Getting Started */}
        <Paper elevation={2} sx={{ p: 4, borderRadius: 2 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            ¡Bienvenido a Expense Tracker PY!
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Comienza a gestionar tus finanzas en guaraníes paraguayos
          </Typography>
          
          <Typography variant="body2" color="text.secondary" paragraph>
            Para empezar, puedes:
          </Typography>
          
          <Box component="ul" sx={{ pl: 2, mb: 3 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              Agregar tu primera transacción
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Configurar categorías personalizadas
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Establecer un presupuesto mensual
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Crear metas de ahorro
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Agregar tus tarjetas de crédito
            </Typography>
          </Box>
          
          <Button
            variant="contained"
            startIcon={<Add />}
            size="large"
            sx={{ fontWeight: 600 }}
          >
            Agregar Primera Transacción
          </Button>
        </Paper>
      </Container>
    </Box>
  );
}
