# Expense Tracker en Guaraníes - Esquema Completo

## 🏗️ Esquema de Base de Datos (Supabase/PostgreSQL)

### 1. Tabla: auth.users (Supabase Auth - Ya existente)
```sql
-- Mantenemos la tabla de auth nativa de Supabase
-- Solo agregamos metadata personalizada
```

### 2. Tabla: user_profiles
```sql
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT NOT NULL,
    email TEXT NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    salary_amount BIGINT, -- En guaraníes (sin decimales)
    salary_frequency TEXT CHECK (salary_frequency IN ('monthly', 'biweekly', 'weekly')),
    currency TEXT DEFAULT 'PYG',
    timezone TEXT DEFAULT 'America/Asuncion',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own profile" ON user_profiles
    USING (auth.uid() = id);
```

### 3. Tabla: credit_cards
```sql
CREATE TABLE credit_cards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    card_name TEXT NOT NULL, -- "Visa Personal", "MasterCard Empresarial"
    last_four_digits TEXT NOT NULL,
    credit_limit BIGINT NOT NULL, -- En guaraníes
    current_balance BIGINT DEFAULT 0, -- Deuda actual
    available_credit BIGINT GENERATED ALWAYS AS (credit_limit - current_balance) STORED,
    closing_day INTEGER CHECK (closing_day >= 1 AND closing_day <= 31),
    due_date INTEGER CHECK (due_date >= 1 AND due_date <= 31),
    minimum_payment BIGINT DEFAULT 0,
    interest_rate DECIMAL(5,2), -- 18.50% anual
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE credit_cards ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own credit cards" ON credit_cards
    USING (auth.uid() = user_id);
```

### 4. Tabla: loans
```sql
CREATE TABLE loans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    loan_name TEXT NOT NULL, -- "Préstamo Personal Itaú"
    loan_type TEXT CHECK (loan_type IN ('personal', 'mortgage', 'auto', 'education', 'other')),
    original_amount BIGINT NOT NULL, -- Monto original
    current_balance BIGINT NOT NULL, -- Saldo actual
    monthly_payment BIGINT NOT NULL,
    interest_rate DECIMAL(5,2),
    remaining_payments INTEGER,
    next_payment_date DATE,
    bank_institution TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE loans ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own loans" ON loans
    USING (auth.uid() = user_id);
```

### 5. Tabla: categories
```sql
CREATE TABLE categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    color TEXT DEFAULT '#6366F1', -- Hex color
    icon TEXT DEFAULT 'shopping-cart', -- Lucide icon name
    category_type TEXT CHECK (category_type IN ('expense', 'income')) DEFAULT 'expense',
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own categories" ON categories
    USING (auth.uid() = user_id);

-- Categorías por defecto que se crean para cada usuario
INSERT INTO categories (user_id, name, color, icon, is_default) VALUES
-- Gastos
(NEW.id, 'Alimentación', '#ef4444', 'utensils', true),
(NEW.id, 'Transporte', '#f59e0b', 'car', true),
(NEW.id, 'Entretenimiento', '#8b5cf6', 'film', true),
(NEW.id, 'Servicios', '#06b6d4', 'zap', true),
(NEW.id, 'Salud', '#10b981', 'heart', true),
(NEW.id, 'Educación', '#3b82f6', 'book', true),
(NEW.id, 'Compras', '#f97316', 'shopping-bag', true),
-- Ingresos
(NEW.id, 'Salario', '#22c55e', 'dollar-sign', true),
(NEW.id, 'Freelance', '#84cc16', 'briefcase', true),
(NEW.id, 'Otros Ingresos', '#06b6d4', 'plus-circle', true);
```

### 6. Tabla: transactions
```sql
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    credit_card_id UUID REFERENCES credit_cards(id) ON DELETE SET NULL,
    amount BIGINT NOT NULL, -- En guaraníes
    description TEXT NOT NULL,
    transaction_type TEXT CHECK (transaction_type IN ('income', 'expense', 'transfer')) NOT NULL,
    payment_method TEXT CHECK (payment_method IN ('cash', 'credit_card', 'debit_card', 'transfer', 'other')),
    transaction_date DATE NOT NULL DEFAULT CURRENT_DATE,
    location TEXT,
    notes TEXT,
    is_recurring BOOLEAN DEFAULT false,
    recurring_frequency TEXT CHECK (recurring_frequency IN ('daily', 'weekly', 'monthly', 'yearly')),
    tags TEXT[], -- Array de tags
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own transactions" ON transactions
    USING (auth.uid() = user_id);

-- Índices para mejor performance
CREATE INDEX idx_transactions_user_date ON transactions(user_id, transaction_date DESC);
CREATE INDEX idx_transactions_category ON transactions(category_id);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
```

### 7. Tabla: budgets
```sql
CREATE TABLE budgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    budget_amount BIGINT NOT NULL, -- Presupuesto mensual
    spent_amount BIGINT DEFAULT 0, -- Se actualiza automáticamente
    budget_period TEXT CHECK (budget_period IN ('weekly', 'monthly', 'yearly')) DEFAULT 'monthly',
    start_date DATE NOT NULL,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    alert_threshold DECIMAL(3,2) DEFAULT 0.80, -- 80% = alerta
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, category_id, budget_period)
);

-- RLS Policy
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own budgets" ON budgets
    USING (auth.uid() = user_id);
```

### 8. Tabla: financial_goals
```sql
CREATE TABLE financial_goals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    goal_name TEXT NOT NULL,
    target_amount BIGINT NOT NULL, -- Meta en guaraníes
    current_amount BIGINT DEFAULT 0, -- Progreso actual
    target_date DATE,
    goal_type TEXT CHECK (goal_type IN ('savings', 'debt_payment', 'purchase', 'emergency_fund', 'investment')),
    priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
    is_achieved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE financial_goals ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own goals" ON financial_goals
    USING (auth.uid() = user_id);
```

### 9. Tabla: monthly_summaries (Para reportes optimizados)
```sql
CREATE TABLE monthly_summaries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    total_income BIGINT DEFAULT 0,
    total_expenses BIGINT DEFAULT 0,
    net_amount BIGINT GENERATED ALWAYS AS (total_income - total_expenses) STORED,
    transactions_count INTEGER DEFAULT 0,
    largest_expense BIGINT DEFAULT 0,
    top_category_expense TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, year, month)
);

-- RLS Policy
ALTER TABLE monthly_summaries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only access their own summaries" ON monthly_summaries
    USING (auth.uid() = user_id);
```

---

## 🎯 Plan de Desarrollo por Fases

### **FASE 1: Fundación y Autenticación (Semanas 1-2)** ✅ **COMPLETADA**

#### Task 1.1: Setup del Proyecto ✅
```bash
# Comandos iniciales
npx create-next-app@15 expense-tracker --typescript --tailwind --app
cd expense-tracker
npm install @supabase/supabase-js @supabase/ssr
npm install @mui/material @emotion/react @emotion/styled @mui/icons-material
npm install react-hook-form @hookform/resolvers zod
npm install recharts date-fns clsx tailwind-merge class-variance-authority
```

#### Task 1.2: Configuración de Supabase ✅
- [x] Crear proyecto en Supabase
- [x] Configurar variables de entorno (.env.local)
- [x] Implementar cliente de Supabase (browser y server)
- [x] Configurar middleware para auth

#### Task 1.3: Sistema de Autenticación Completo ✅
- [x] Página de Login con validaciones
- [x] Página de Register
- [x] Forgot Password flow
- [x] Email confirmation (callback route)
- [x] Logout funcional
- [x] Protected routes middleware
- [x] Session management (useAuth hook)

#### Task 1.4: Seguridad Básica ✅
- [x] Row Level Security (RLS) en todas las tablas
- [x] API routes con validación de auth
- [x] CSRF protection (middleware)
- [x] Rate limiting básico

#### Task 1.5: Estructura Base ✅
- [x] Estructura de directorios organizada
- [x] Tipos TypeScript completos
- [x] Utilidades de formateo y validación
- [x] Componentes UI base (Button, Input, Card)
- [x] Hooks personalizados (useAuth, useLocalStorage, useDebounce)
- [x] Sistema de constantes y configuraciones

#### Task 1.6: Sistema de Diseño ✅
- [x] Paleta de colores profesional y minimalista
- [x] Sistema de colores para categorías de gastos
- [x] Variables CSS para tema claro y oscuro
- [x] Gradientes y sombras profesionales
- [x] Documentación completa del design system
- [x] Aplicación del nuevo diseño en páginas principales

**Deliverable Fase 1:** ✅ Sistema de auth completo y seguro + Design System profesional

---

### **FASE 2: Perfiles y Configuración Base (Semana 3)**

#### Task 2.1: Perfil de Usuario
- [ ] Página de perfil editable
- [ ] Upload de avatar (Supabase Storage)
- [ ] Configuración de salario
- [ ] Configuración de zona horaria

#### Task 2.2: Onboarding Inicial
- [ ] Wizard de configuración inicial
- [ ] Creación automática de categorías por defecto
- [ ] Tutorial interactivo básico

**Deliverable Fase 2:** Gestión completa de perfiles

---

### **FASE 3: Gestión de Tarjetas de Crédito (Semana 4)**

#### Task 3.1: CRUD de Tarjetas
- [ ] Agregar nueva tarjeta de crédito
- [ ] Lista de tarjetas con estados
- [ ] Editar información de tarjeta
- [ ] Eliminar tarjeta (soft delete)

#### Task 3.2: Dashboard de Crédito
- [ ] Vista general de límites vs uso
- [ ] Alertas de límite cercano
- [ ] Calendario de vencimientos
- [ ] Cálculo de pagos mínimos

**Deliverable Fase 3:** Gestión completa de tarjetas de crédito

---

### **FASE 4: Sistema de Préstamos (Semana 5)**

#### Task 4.1: Gestión de Préstamos
- [ ] Agregar préstamos con amortización
- [ ] Vista de préstamos activos
- [ ] Simulador de pagos
- [ ] Tracking de pagos realizados

#### Task 4.2: Alertas y Recordatorios
- [ ] Notificaciones de próximos vencimientos
- [ ] Sistema de alertas configurable

**Deliverable Fase 4:** Sistema completo de préstamos

---

### **FASE 5: Transacciones y Categorías (Semanas 6-7)**

#### Task 5.1: Sistema de Categorías
- [ ] CRUD completo de categorías
- [ ] Categorías personalizadas con colores/íconos
- [ ] Importación/exportación de categorías

#### Task 5.2: Gestión de Transacciones
- [ ] Agregar transacciones (ingresos/gastos)
- [ ] Lista paginada y filtrable
- [ ] Búsqueda avanzada
- [ ] Bulk operations (editar/eliminar múltiples)

#### Task 5.3: Importación de Datos
- [ ] Import desde CSV/Excel
- [ ] Mapeo automático de categorías
- [ ] Validación de duplicados

**Deliverable Fase 5:** Sistema completo de transacciones

---

### **FASE 6: Presupuestos y Metas (Semana 8)**

#### Task 6.1: Sistema de Presupuestos
- [ ] Crear presupuestos por categoría
- [ ] Tracking automático vs gastos reales
- [ ] Alertas de presupuesto excedido
- [ ] Vista comparativa mensual

#### Task 6.2: Metas Financieras
- [ ] Crear metas de ahorro
- [ ] Tracking de progreso
- [ ] Proyecciones de cumplimiento

**Deliverable Fase 6:** Gestión de presupuestos y metas

---

### **FASE 7: Analytics y Reportes (Semana 9)**

#### Task 7.1: Dashboard Principal
- [ ] Resumen financiero del mes
- [ ] Gráficos de gastos por categoría
- [ ] Tendencias temporales
- [ ] KPIs principales (cashflow, etc.)

#### Task 7.2: Reportes Avanzados
- [ ] Reportes mensuales/anuales
- [ ] Exportación a PDF
- [ ] Comparativas período vs período
- [ ] Análisis de patrones de gasto

**Deliverable Fase 7:** Sistema completo de reportes

---

### **FASE 8: UX/UI Avanzada y Optimización (Semana 10)**

#### Task 8.1: Mejoras de UI
- [ ] Dark/Light mode
- [ ] Responsive design completo
- [ ] Animaciones y micro-interacciones
- [ ] PWA capabilities

#### Task 8.2: Performance y SEO
- [ ] Optimización de queries
- [ ] Lazy loading
- [ ] Caching strategies
- [ ] SEO básico

#### Task 8.3: Testing
- [ ] Unit tests críticos
- [ ] Integration tests de auth
- [ ] E2E tests principales flows

**Deliverable Fase 8:** App lista para producción

---

### **FASE 9: Features Avanzadas (Semana 11)**

#### Task 9.1: Transacciones Recurrentes
- [ ] Setup de gastos/ingresos recurrentes
- [ ] Procesamiento automático
- [ ] Gestión de excepciones

#### Task 9.2: Integraciones
- [ ] API para conectar con bancos (si disponible)
- [ ] Webhooks para notificaciones
- [ ] Export/Import avanzado

**Deliverable Fase 9:** Features premium implementadas

---

### **FASE 10: Deployment y Monitoring (Semana 12)**

#### Task 10.1: Deployment
- [ ] Setup en Vercel/Netlify
- [ ] CI/CD pipeline
- [ ] Environment management
- [ ] SSL y security headers

#### Task 10.2: Monitoring y Analytics
- [ ] Error tracking (Sentry)
- [ ] User analytics básicas
- [ ] Performance monitoring
- [ ] Backup strategies

#### Task 10.3: Documentación
- [ ] README completo
- [ ] API documentation
- [ ] User manual básico
- [ ] Deployment guide

**Deliverable Final:** Aplicación completa en producción

---

## 🛡️ Consideraciones de Seguridad

### Implementaciones Críticas:
1. **JWT con secret rotation**: Tokens que expiran y se renuevan
2. **API rate limiting**: Por usuario y por endpoint
3. **Input validation**: Zod schemas en todos los formularios
4. **SQL injection prevention**: Queries parametrizadas
5. **XSS protection**: Sanitización de outputs
6. **RBAC**: Row Level Security estricto
7. **Audit logs**: Tracking de cambios sensibles
8. **Encryption**: Datos sensibles encriptados en DB

### Estructura de Seguridad Sugerida:
```typescript
// middleware/auth.ts - Validación en cada request
// lib/security.ts - Helpers de encriptación
// lib/validation.ts - Schemas de validación
// api/auth/* - Endpoints seguros
```

## 📱 Stack Tecnológico Final

- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS + Material UI
- **Backend**: Next.js API Routes + Supabase
- **Database**: PostgreSQL (Supabase)
- **Auth**: Supabase Auth + RLS
- **Storage**: Supabase Storage
- **Charts**: Recharts
- **Forms**: React Hook Form + Zod
- **Icons**: Material UI Icons
- **Deployment**: Vercel
- **Monitoring**: Vercel Analytics + Sentry
