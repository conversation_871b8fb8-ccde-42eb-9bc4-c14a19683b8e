/**
 * Traduce mensajes de error de Supabase del inglés al español
 */
export function translateAuthError(errorMessage: string): string {
  const errorTranslations: Record<string, string> = {
    // Auth errors
    'Invalid login credentials': 'Credenciales de inicio de sesión inválidas',
    'Email not confirmed': 'Email no confirmado. Revisa tu bandeja de entrada.',
    'Invalid email': 'Email inválido',
    'Password should be at least 6 characters': 'La contraseña debe tener al menos 6 caracteres',
    'User already registered': 'El usuario ya está registrado',
    'Email already registered': 'El email ya está registrado',
    'Signup is disabled': 'El registro está deshabilitado',
    'Email rate limit exceeded': 'Límite de emails excedido. Intenta más tarde.',
    'Password is too weak': 'La contraseña es muy débil',
    'Invalid password': 'Contraseña inválida',
    'User not found': 'Usuario no encontrado',
    'Email link is invalid or has expired': 'El enlace del email es inválido o ha expirado',
    'Token has expired or is invalid': 'El token ha expirado o es inválido',
    'Unable to validate email address: invalid format': 'No se puede validar la dirección de email: formato inválido',
    'Password reset is disabled': 'El restablecimiento de contraseña está deshabilitado',
    'For security purposes, you can only request this once every 60 seconds': 'Por seguridad, solo puedes solicitar esto una vez cada 60 segundos',
    
    // Database errors
    'Cannot coerce the result to a single JSON object': 'No se pudo obtener el perfil del usuario',
    'The result contains 0 rows': 'No se encontraron datos del usuario',
    'duplicate key value violates unique constraint': 'Ya existe un registro con estos datos',
    'violates foreign key constraint': 'Error de referencia en la base de datos',
    'permission denied': 'Permisos insuficientes',
    'row-level security policy': 'Error de permisos de seguridad',
    
    // Network errors
    'Failed to fetch': 'Error de conexión. Verifica tu internet.',
    'Network request failed': 'Error de red. Intenta nuevamente.',
    'Request timeout': 'Tiempo de espera agotado. Intenta nuevamente.',
    
    // Generic errors
    'Something went wrong': 'Algo salió mal. Intenta nuevamente.',
    'An error occurred': 'Ocurrió un error. Intenta nuevamente.',
    'Unexpected error': 'Error inesperado. Intenta nuevamente.',
  };

  // Buscar traducción exacta
  if (errorTranslations[errorMessage]) {
    return errorTranslations[errorMessage];
  }

  // Buscar traducción parcial (contiene la palabra clave)
  for (const [englishError, spanishError] of Object.entries(errorTranslations)) {
    if (errorMessage.toLowerCase().includes(englishError.toLowerCase())) {
      return spanishError;
    }
  }

  // Si no se encuentra traducción, devolver mensaje genérico en español
  return 'Error inesperado. Por favor intenta nuevamente.';
}

/**
 * Traduce mensajes de validación de formularios
 */
export function translateValidationError(errorMessage: string): string {
  const validationTranslations: Record<string, string> = {
    'Required': 'Este campo es requerido',
    'Invalid email': 'Email inválido',
    'String must contain at least 8 character(s)': 'Debe contener al menos 8 caracteres',
    'String must contain at least 6 character(s)': 'Debe contener al menos 6 caracteres',
    'String must contain at least 2 character(s)': 'Debe contener al menos 2 caracteres',
    'Passwords do not match': 'Las contraseñas no coinciden',
    'Invalid format': 'Formato inválido',
    'Too short': 'Muy corto',
    'Too long': 'Muy largo',
  };

  if (validationTranslations[errorMessage]) {
    return validationTranslations[errorMessage];
  }

  return errorMessage;
}

/**
 * Obtiene un mensaje de éxito traducido
 */
export function getSuccessMessage(action: string): string {
  const successMessages: Record<string, string> = {
    'signup': '¡Cuenta creada exitosamente! Revisa tu email para confirmar tu cuenta.',
    'login': '¡Bienvenido de vuelta!',
    'logout': '¡Sesión cerrada exitosamente!',
    'password_reset': 'Se ha enviado un enlace de restablecimiento a tu email.',
    'password_update': '¡Contraseña actualizada exitosamente!',
    'profile_update': '¡Perfil actualizado exitosamente!',
    'email_confirmed': '¡Email confirmado exitosamente!',
  };

  return successMessages[action] || '¡Operación completada exitosamente!';
}
