# Expense Tracker PY 💰

Un gestor de gastos personal desarrollado específicamente para Paraguay, con soporte completo para guaraníes paraguayos (PYG).

## 🚀 Características

- **Gestión de Transacciones**: Registro de ingresos y gastos con categorización
- **Tarjetas de Crédito**: Seguimiento de límites, balances y fechas de vencimiento
- **Préstamos**: Gestión de préstamos personales con amortización
- **Presupuestos**: Creación y seguimiento de presupuestos por categoría
- **Metas Financieras**: Establecimiento y seguimiento de objetivos de ahorro
- **Reportes**: Análisis detallado de patrones de gasto
- **Autenticación Segura**: Sistema completo de autenticación con Supabase
- **Responsive Design**: Optimizado para móviles y escritorio

## 🛠️ Stack Tecnológico

- **Frontend**: Next.js 15 + TypeScript + Material-UI
- **Backend**: Next.js API Routes + Supabase
- **Base de Datos**: PostgreSQL (Supabase)
- **Autenticación**: Supabase Auth + Row Level Security
- **Formularios**: React Hook Form + Zod
- **Gráficos**: Recharts
- **UI Components**: Material-UI + Material Design Icons

## 📋 Requisitos Previos

- Node.js 18+
- npm o yarn
- Cuenta de Supabase

## 🔧 Configuración

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd expense-tracker
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
cp .env.local.example .env.local
```

Edita `.env.local` con tus credenciales de Supabase:
```env
NEXT_PUBLIC_SUPABASE_URL=tu_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=tu_service_role_key
```

4. **Configurar la base de datos**
- Ejecuta los scripts SQL del archivo `proyecto.md` en tu proyecto de Supabase
- Configura las políticas RLS según el esquema

5. **Ejecutar en desarrollo**
```bash
npm run dev
```

Abre [http://localhost:3000](http://localhost:3000) en tu navegador.

## 📁 Estructura del Proyecto

```
src/
├── app/                    # App Router de Next.js
│   ├── auth/              # Páginas de autenticación
│   ├── dashboard/         # Páginas del dashboard
│   └── api/               # API Routes
├── components/            # Componentes React
│   ├── ui/               # Componentes UI base
│   ├── forms/            # Formularios
│   ├── charts/           # Gráficos
│   └── layout/           # Componentes de layout
├── lib/                  # Configuraciones y utilidades
├── hooks/                # Custom hooks
├── types/                # Definiciones de tipos
└── utils/                # Funciones utilitarias
```

## 🔐 Seguridad

- **Row Level Security (RLS)**: Todas las tablas tienen políticas RLS
- **Validación de entrada**: Esquemas Zod en todos los formularios
- **Middleware de autenticación**: Protección de rutas
- **Sanitización**: Limpieza de inputs del usuario

## 🌍 Localización

- **Moneda**: Guaraníes paraguayos (₲)
- **Formato de fecha**: DD/MM/YYYY
- **Zona horaria**: America/Asuncion
- **Idioma**: Español (Paraguay)

## 📱 Características Móviles

- Diseño responsive
- Optimizado para touch
- PWA ready (próximamente)

## 🚀 Deployment

### Vercel (Recomendado)

1. Conecta tu repositorio a Vercel
2. Configura las variables de entorno
3. Deploy automático

### Manual

```bash
npm run build
npm start
```

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 🆘 Soporte

Si tienes problemas o preguntas:

1. Revisa la documentación en `proyecto.md`
2. Busca en los issues existentes
3. Crea un nuevo issue con detalles del problema

## 🗺️ Roadmap

- [ ] Fase 1: Autenticación y perfiles ✅
- [ ] Fase 2: Gestión de transacciones
- [ ] Fase 3: Tarjetas de crédito
- [ ] Fase 4: Préstamos
- [ ] Fase 5: Presupuestos y metas
- [ ] Fase 6: Reportes y analytics
- [ ] Fase 7: PWA y notificaciones
- [ ] Fase 8: Integraciones bancarias

---

Desarrollado con ❤️ para la comunidad paraguaya
