// Database Types
export type { Database } from './database';

export interface UserProfile {
  id: string;
  full_name: string;
  email: string;
  avatar_url?: string;
  phone?: string;
  salary_amount?: number; // En guaraníes
  salary_frequency?: 'monthly' | 'biweekly' | 'weekly';
  currency: string;
  timezone: string;
  created_at: string;
  updated_at: string;
}

export interface CreditCard {
  id: string;
  user_id: string;
  card_name: string;
  last_four_digits: string;
  credit_limit: number; // En guaraníes
  current_balance: number;
  available_credit: number;
  closing_day: number;
  due_date: number;
  minimum_payment: number;
  interest_rate: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Loan {
  id: string;
  user_id: string;
  loan_name: string;
  loan_type: 'personal' | 'mortgage' | 'auto' | 'education' | 'other';
  original_amount: number;
  current_balance: number;
  monthly_payment: number;
  interest_rate: number;
  remaining_payments: number;
  next_payment_date: string;
  bank_institution?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  user_id: string;
  name: string;
  color: string;
  icon: string;
  category_type: 'expense' | 'income';
  is_default: boolean;
  created_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  category_id?: string;
  credit_card_id?: string;
  amount: number; // En guaraníes
  description: string;
  transaction_type: 'income' | 'expense' | 'transfer';
  payment_method: 'cash' | 'credit_card' | 'debit_card' | 'transfer' | 'other';
  transaction_date: string;
  location?: string;
  notes?: string;
  is_recurring: boolean;
  recurring_frequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  tags?: string[];
  created_at: string;
  updated_at: string;
  // Relations
  category?: Category;
  credit_card?: CreditCard;
}

export interface Budget {
  id: string;
  user_id: string;
  category_id: string;
  name: string;
  budget_amount: number;
  spent_amount: number;
  budget_period: 'weekly' | 'monthly' | 'yearly';
  start_date: string;
  end_date?: string;
  is_active: boolean;
  alert_threshold: number;
  created_at: string;
  updated_at: string;
  // Relations
  category?: Category;
}

export interface FinancialGoal {
  id: string;
  user_id: string;
  goal_name: string;
  target_amount: number;
  current_amount: number;
  target_date?: string;
  goal_type: 'savings' | 'debt_payment' | 'purchase' | 'emergency_fund' | 'investment';
  priority: 'low' | 'medium' | 'high';
  is_achieved: boolean;
  created_at: string;
  updated_at: string;
}

export interface MonthlySummary {
  id: string;
  user_id: string;
  year: number;
  month: number;
  total_income: number;
  total_expenses: number;
  net_amount: number;
  transactions_count: number;
  largest_expense: number;
  top_category_expense?: string;
  created_at: string;
  updated_at: string;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
}

export interface TransactionForm {
  amount: number;
  description: string;
  category_id: string;
  transaction_type: 'income' | 'expense';
  payment_method: 'cash' | 'credit_card' | 'debit_card' | 'transfer' | 'other';
  credit_card_id?: string;
  transaction_date: string;
  location?: string;
  notes?: string;
  tags?: string[];
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// UI Types
export interface DashboardStats {
  totalIncome: number;
  totalExpenses: number;
  netAmount: number;
  transactionsCount: number;
  budgetUtilization: number;
  creditUtilization: number;
}

export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

// Utility Types
export type SortOrder = 'asc' | 'desc';
export type DateRange = {
  from: Date;
  to: Date;
};

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: SortOrder;
}

export interface FilterParams {
  category_id?: string;
  transaction_type?: 'income' | 'expense';
  payment_method?: string;
  date_range?: DateRange;
  search?: string;
}
