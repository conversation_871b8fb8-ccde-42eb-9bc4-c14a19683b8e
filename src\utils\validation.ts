import { z } from 'zod';
import { VALIDATION_RULES, ERROR_MESSAGES } from '@/lib/constants';

// Esquemas de validación base
export const emailSchema = z
  .string()
  .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
  .email(ERROR_MESSAGES.INVALID_EMAIL);

export const passwordSchema = z
  .string()
  .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH, ERROR_MESSAGES.PASSWORD_TOO_SHORT);

export const phoneSchema = z
  .string()
  .optional()
  .refine(
    (val) => !val || VALIDATION_RULES.PHONE_REGEX.test(val),
    ERROR_MESSAGES.INVALID_PHONE
  );

export const amountSchema = z
  .number()
  .min(VALIDATION_RULES.AMOUNT_MIN, ERROR_MESSAGES.AMOUNT_TOO_LOW)
  .max(VALIDATION_RULES.AMOUNT_MAX, ERROR_MESSAGES.AMOUNT_TOO_HIGH);

export const descriptionSchema = z
  .string()
  .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
  .max(VALIDATION_RULES.DESCRIPTION_MAX_LENGTH, ERROR_MESSAGES.DESCRIPTION_TOO_LONG);

export const nameSchema = z
  .string()
  .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
  .max(VALIDATION_RULES.NAME_MAX_LENGTH, 'El nombre es demasiado largo');

// Esquemas de formularios
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
});

export const registerSchema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
    fullName: nameSchema,
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: ERROR_MESSAGES.PASSWORDS_DONT_MATCH,
    path: ['confirmPassword'],
  });

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: ERROR_MESSAGES.PASSWORDS_DONT_MATCH,
    path: ['confirmPassword'],
  });

export const profileSchema = z.object({
  full_name: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  salary_amount: z.number().optional(),
  salary_frequency: z.enum(['monthly', 'biweekly', 'weekly']).optional(),
});

export const transactionSchema = z.object({
  amount: amountSchema,
  description: descriptionSchema,
  category_id: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  transaction_type: z.enum(['income', 'expense']),
  payment_method: z.enum(['cash', 'credit_card', 'debit_card', 'transfer', 'other']),
  credit_card_id: z.string().optional(),
  transaction_date: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  location: z.string().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

export const categorySchema = z.object({
  name: nameSchema,
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Color inválido'),
  icon: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  category_type: z.enum(['expense', 'income']),
});

export const creditCardSchema = z.object({
  card_name: nameSchema,
  last_four_digits: z
    .string()
    .length(4, 'Debe tener exactamente 4 dígitos')
    .regex(/^\d{4}$/, 'Solo se permiten números'),
  credit_limit: amountSchema,
  closing_day: z.number().min(1).max(31),
  due_date: z.number().min(1).max(31),
  interest_rate: z.number().min(0).max(100),
});

export const loanSchema = z.object({
  loan_name: nameSchema,
  loan_type: z.enum(['personal', 'mortgage', 'auto', 'education', 'other']),
  original_amount: amountSchema,
  current_balance: amountSchema,
  monthly_payment: amountSchema,
  interest_rate: z.number().min(0).max(100),
  remaining_payments: z.number().min(0),
  next_payment_date: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  bank_institution: z.string().optional(),
});

export const budgetSchema = z.object({
  name: nameSchema,
  category_id: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  budget_amount: amountSchema,
  budget_period: z.enum(['weekly', 'monthly', 'yearly']),
  start_date: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  end_date: z.string().optional(),
  alert_threshold: z.number().min(0).max(1),
});

export const financialGoalSchema = z.object({
  goal_name: nameSchema,
  target_amount: amountSchema,
  target_date: z.string().optional(),
  goal_type: z.enum(['savings', 'debt_payment', 'purchase', 'emergency_fund', 'investment']),
  priority: z.enum(['low', 'medium', 'high']),
});

// Tipos inferidos de los esquemas
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type ProfileFormData = z.infer<typeof profileSchema>;
export type TransactionFormData = z.infer<typeof transactionSchema>;
export type CategoryFormData = z.infer<typeof categorySchema>;
export type CreditCardFormData = z.infer<typeof creditCardSchema>;
export type LoanFormData = z.infer<typeof loanSchema>;
export type BudgetFormData = z.infer<typeof budgetSchema>;
export type FinancialGoalFormData = z.infer<typeof financialGoalSchema>;

// Funciones de validación utilitarias
export function validateEmail(email: string): boolean {
  return VALIDATION_RULES.EMAIL_REGEX.test(email);
}

export function validatePhone(phone: string): boolean {
  return VALIDATION_RULES.PHONE_REGEX.test(phone);
}

export function validatePassword(password: string): boolean {
  return password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH;
}

export function validateAmount(amount: number): boolean {
  return amount >= VALIDATION_RULES.AMOUNT_MIN && amount <= VALIDATION_RULES.AMOUNT_MAX;
}

export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export function validateCardNumber(cardNumber: string): boolean {
  // Validación básica de número de tarjeta usando algoritmo de Luhn
  const cleaned = cardNumber.replace(/\D/g, '');
  
  if (cleaned.length < 13 || cleaned.length > 19) {
    return false;
  }
  
  let sum = 0;
  let isEven = false;
  
  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i]);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
}

export function validateDateRange(startDate: string, endDate: string): boolean {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  return start <= end;
}

export function validateFutureDate(date: string): boolean {
  const inputDate = new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return inputDate >= today;
}

export function validatePastDate(date: string): boolean {
  const inputDate = new Date(date);
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  
  return inputDate <= today;
}
