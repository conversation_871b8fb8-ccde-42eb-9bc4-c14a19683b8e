# Sistema de Diseño - Expense Tracker PY

## 🎨 Filosofía de Diseño

El sistema de diseño de Expense Tracker PY se basa en principios de **minimalismo profesional** y **confiabilidad**, diseñado específicamente para aplicaciones financieras que requieren claridad, legibilidad y confianza.

### Principios Fundamentales

1. **Minimalismo Profesional**: Diseño limpio sin elementos innecesarios
2. **Confiabilidad**: Colores que transmiten seguridad y profesionalismo
3. **Accesibilidad**: Contraste adecuado y legibilidad óptima
4. **Consistencia**: Paleta coherente en toda la aplicación
5. **Localización**: Adaptado para la cultura paraguaya

## 🎯 Paleta de Colores

### Colores Primarios - Azul Profesional
```css
/* Azul principal - Color primario de la marca */
--primary-50: #f0f9ff   /* Fondos muy claros */
--primary-100: #e0f2fe  /* Hover states suaves */
--primary-200: #bae6fd  /* Borders y dividers */
--primary-300: #7dd3fc  /* Elementos secundarios */
--primary-400: #38bdf8  /* Accents vibrantes */
--primary-500: #0ea5e9  /* COLOR PRINCIPAL */
--primary-600: #0284c7  /* Hover states */
--primary-700: #0369a1  /* Texto sobre fondos claros */
--primary-800: #075985  /* Elementos importantes */
--primary-900: #0c4a6e  /* Texto crítico */
```

**Uso del Azul:**
- Transmite confianza y profesionalismo
- Asociado con instituciones financieras
- Excelente legibilidad y contraste
- No es agresivo visualmente

### Colores Neutros - Grises Profesionales
```css
/* Escala de grises para texto y fondos */
--neutral-50: #fafafa   /* Fondos principales */
--neutral-100: #f5f5f5  /* Cards y contenedores */
--neutral-200: #e5e5e5  /* Borders sutiles */
--neutral-300: #d4d4d4  /* Dividers */
--neutral-400: #a3a3a3  /* Texto secundario */
--neutral-500: #737373  /* Texto normal */
--neutral-600: #525252  /* Texto importante */
--neutral-700: #404040  /* Headings */
--neutral-800: #262626  /* Texto principal */
--neutral-900: #171717  /* Texto crítico */
```

### Colores de Estado
```css
/* Verde - Éxito, Ingresos, Positivo */
--success-50: #f0fdf4
--success-500: #22c55e  /* Verde principal */
--success-600: #16a34a

/* Rojo - Error, Gastos, Negativo */
--error-50: #fef2f2
--error-500: #ef4444    /* Rojo principal */
--error-600: #dc2626

/* Amarillo - Advertencia, Pendiente */
--warning-50: #fffbeb
--warning-500: #f59e0b  /* Amarillo principal */
--warning-600: #d97706

/* Azul Info - Información, Neutral */
--info-50: #f0f9ff
--info-500: #3b82f6    /* Azul info */
--info-600: #2563eb
```

## 🏷️ Categorías de Gastos - Colores Específicos

### Gastos Esenciales (Tonos Azules)
- **Alimentación**: `#0ea5e9` (Azul principal)
- **Transporte**: `#0284c7` (Azul oscuro)
- **Servicios**: `#075985` (Azul muy oscuro)
- **Salud**: `#22c55e` (Verde - positivo)

### Gastos Personales (Tonos Cálidos Profesionales)
- **Entretenimiento**: `#8b5cf6` (Púrpura suave)
- **Compras**: `#f59e0b` (Amarillo/naranja)
- **Educación**: `#3b82f6` (Azul info)
- **Ropa**: `#ec4899` (Rosa profesional)

### Gastos del Hogar (Tonos Neutros)
- **Hogar**: `#737373` (Gris medio)
- **Mantenimiento**: `#525252` (Gris oscuro)

### Ingresos (Tonos Verdes)
- **Salario**: `#16a34a` (Verde principal)
- **Freelance**: `#15803d` (Verde oscuro)
- **Inversiones**: `#059669` (Verde esmeralda)
- **Otros Ingresos**: `#10b981` (Verde claro)

## 🎨 Gradientes y Efectos

### Gradientes de Fondo
```css
/* Gradiente principal de la app */
background: linear-gradient(to bottom right, #f9fafb, #ffffff, #f0f9ff);

/* Gradiente para hero sections */
background: linear-gradient(to bottom right, #f0f9ff, #ffffff, #f0f9ff);

/* Gradiente para cards */
background: linear-gradient(to bottom, #ffffff, #f9fafb);
```

### Sombras Profesionales
```css
/* Sombra para cards */
box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);

/* Sombra para elementos elevados */
box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);

/* Sombra para modales */
box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
```

## 📐 Espaciado y Tipografía

### Sistema de Espaciado
```css
--spacing-xs: 0.25rem   /* 4px */
--spacing-sm: 0.5rem    /* 8px */
--spacing-md: 1rem      /* 16px */
--spacing-lg: 1.5rem    /* 24px */
--spacing-xl: 2rem      /* 32px */
--spacing-2xl: 3rem     /* 48px */
--spacing-3xl: 4rem     /* 64px */
```

### Bordes Redondeados
```css
--radius-sm: 0.25rem    /* 4px */
--radius-md: 0.375rem   /* 6px */
--radius-lg: 0.5rem     /* 8px */
--radius-xl: 0.75rem    /* 12px */
--radius-2xl: 1rem      /* 16px */
```

### Tipografía
```css
/* Familia de fuentes */
font-family: 'Inter', system-ui, sans-serif;

/* Tamaños de texto */
--text-xs: 0.75rem      /* 12px */
--text-sm: 0.875rem     /* 14px */
--text-base: 1rem       /* 16px */
--text-lg: 1.125rem     /* 18px */
--text-xl: 1.25rem      /* 20px */
--text-2xl: 1.5rem      /* 24px */
--text-3xl: 1.875rem    /* 30px */
--text-4xl: 2.25rem     /* 36px */

/* Pesos de fuente */
--font-normal: 400
--font-medium: 500
--font-semibold: 600
--font-bold: 700
```

## 🎯 Aplicación de Colores

### Jerarquía de Texto
1. **Texto Principal**: `#262626` (neutral-800)
2. **Texto Secundario**: `#525252` (neutral-600)
3. **Texto Muted**: `#737373` (neutral-500)
4. **Texto Placeholder**: `#a3a3a3` (neutral-400)

### Fondos
1. **Fondo Principal**: `#ffffff` (blanco)
2. **Fondo Secundario**: `#fafafa` (neutral-50)
3. **Fondo de Cards**: `#ffffff` con sombra sutil
4. **Fondo de Inputs**: `#ffffff` con border `#e5e5e5`

### Estados Interactivos
1. **Hover**: Oscurecer 100 puntos en la escala
2. **Active**: Oscurecer 200 puntos en la escala
3. **Focus**: Ring de `#0ea5e9` con opacidad 50%
4. **Disabled**: Opacidad 50% + cursor not-allowed

## 🌙 Modo Oscuro

### Colores para Modo Oscuro
```css
@media (prefers-color-scheme: dark) {
  --background: #171717      /* Fondo principal oscuro */
  --foreground: #fafafa      /* Texto claro */
  --card: #262626           /* Cards oscuras */
  --border: #404040         /* Bordes oscuros */
  --muted: #404040          /* Elementos muted */
  --muted-foreground: #a3a3a3 /* Texto muted claro */
}
```

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile First */
sm: 640px   /* Tablet pequeña */
md: 768px   /* Tablet */
lg: 1024px  /* Desktop pequeño */
xl: 1280px  /* Desktop */
2xl: 1536px /* Desktop grande */
```

## ✅ Checklist de Implementación

### Para cada componente nuevo:
- [ ] Usar colores de la paleta definida
- [ ] Aplicar espaciado consistente
- [ ] Incluir estados hover/focus/active
- [ ] Verificar contraste de accesibilidad
- [ ] Probar en modo claro y oscuro
- [ ] Validar en diferentes tamaños de pantalla

### Accesibilidad:
- [ ] Contraste mínimo 4.5:1 para texto normal
- [ ] Contraste mínimo 3:1 para texto grande
- [ ] Estados de focus visibles
- [ ] Colores no como único indicador de estado

## 🔧 Herramientas de Desarrollo

### Clases CSS Predefinidas
```css
/* Backgrounds */
.bg-primary { background-color: #0ea5e9; }
.bg-secondary { background-color: #f5f5f5; }
.bg-success { background-color: #22c55e; }
.bg-error { background-color: #ef4444; }

/* Text Colors */
.text-primary { color: #262626; }
.text-secondary { color: #525252; }
.text-muted { color: #737373; }
.text-success { color: #16a34a; }
.text-error { color: #dc2626; }

/* Borders */
.border-default { border-color: #e5e5e5; }
.border-primary { border-color: #0ea5e9; }
```

Este sistema de diseño garantiza una experiencia visual coherente, profesional y accesible en toda la aplicación Expense Tracker PY.
