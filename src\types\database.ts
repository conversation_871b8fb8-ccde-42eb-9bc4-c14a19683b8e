// Database types generated from Supabase schema
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          full_name: string
          email: string
          avatar_url: string | null
          phone: string | null
          salary_amount: number | null
          salary_frequency: 'monthly' | 'biweekly' | 'weekly' | null
          currency: string
          timezone: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name: string
          email: string
          avatar_url?: string | null
          phone?: string | null
          salary_amount?: number | null
          salary_frequency?: 'monthly' | 'biweekly' | 'weekly' | null
          currency?: string
          timezone?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          email?: string
          avatar_url?: string | null
          phone?: string | null
          salary_amount?: number | null
          salary_frequency?: 'monthly' | 'biweekly' | 'weekly' | null
          currency?: string
          timezone?: string
          created_at?: string
          updated_at?: string
        }
      }
      credit_cards: {
        Row: {
          id: string
          user_id: string
          card_name: string
          last_four_digits: string
          credit_limit: number
          current_balance: number
          available_credit: number
          closing_day: number
          due_date: number
          minimum_payment: number
          interest_rate: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          card_name: string
          last_four_digits: string
          credit_limit: number
          current_balance?: number
          closing_day: number
          due_date: number
          minimum_payment?: number
          interest_rate: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          card_name?: string
          last_four_digits?: string
          credit_limit?: number
          current_balance?: number
          closing_day?: number
          due_date?: number
          minimum_payment?: number
          interest_rate?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      loans: {
        Row: {
          id: string
          user_id: string
          loan_name: string
          loan_type: 'personal' | 'mortgage' | 'auto' | 'education' | 'other'
          original_amount: number
          current_balance: number
          monthly_payment: number
          interest_rate: number
          remaining_payments: number
          next_payment_date: string
          bank_institution: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          loan_name: string
          loan_type: 'personal' | 'mortgage' | 'auto' | 'education' | 'other'
          original_amount: number
          current_balance: number
          monthly_payment: number
          interest_rate: number
          remaining_payments: number
          next_payment_date: string
          bank_institution?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          loan_name?: string
          loan_type?: 'personal' | 'mortgage' | 'auto' | 'education' | 'other'
          original_amount?: number
          current_balance?: number
          monthly_payment?: number
          interest_rate?: number
          remaining_payments?: number
          next_payment_date?: string
          bank_institution?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          user_id: string
          name: string
          color: string
          icon: string
          category_type: 'expense' | 'income'
          is_default: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          color?: string
          icon?: string
          category_type?: 'expense' | 'income'
          is_default?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          color?: string
          icon?: string
          category_type?: 'expense' | 'income'
          is_default?: boolean
          created_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          credit_card_id: string | null
          amount: number
          description: string
          transaction_type: 'income' | 'expense' | 'transfer'
          payment_method: 'cash' | 'credit_card' | 'debit_card' | 'transfer' | 'other'
          transaction_date: string
          location: string | null
          notes: string | null
          is_recurring: boolean
          recurring_frequency: 'daily' | 'weekly' | 'monthly' | 'yearly' | null
          tags: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          credit_card_id?: string | null
          amount: number
          description: string
          transaction_type: 'income' | 'expense' | 'transfer'
          payment_method: 'cash' | 'credit_card' | 'debit_card' | 'transfer' | 'other'
          transaction_date?: string
          location?: string | null
          notes?: string | null
          is_recurring?: boolean
          recurring_frequency?: 'daily' | 'weekly' | 'monthly' | 'yearly' | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          credit_card_id?: string | null
          amount?: number
          description?: string
          transaction_type?: 'income' | 'expense' | 'transfer'
          payment_method?: 'cash' | 'credit_card' | 'debit_card' | 'transfer' | 'other'
          transaction_date?: string
          location?: string | null
          notes?: string | null
          is_recurring?: boolean
          recurring_frequency?: 'daily' | 'weekly' | 'monthly' | 'yearly' | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      budgets: {
        Row: {
          id: string
          user_id: string
          category_id: string
          name: string
          budget_amount: number
          spent_amount: number
          budget_period: 'weekly' | 'monthly' | 'yearly'
          start_date: string
          end_date: string | null
          is_active: boolean
          alert_threshold: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id: string
          name: string
          budget_amount: number
          spent_amount?: number
          budget_period?: 'weekly' | 'monthly' | 'yearly'
          start_date: string
          end_date?: string | null
          is_active?: boolean
          alert_threshold?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string
          name?: string
          budget_amount?: number
          spent_amount?: number
          budget_period?: 'weekly' | 'monthly' | 'yearly'
          start_date?: string
          end_date?: string | null
          is_active?: boolean
          alert_threshold?: number
          created_at?: string
          updated_at?: string
        }
      }
      financial_goals: {
        Row: {
          id: string
          user_id: string
          goal_name: string
          target_amount: number
          current_amount: number
          target_date: string | null
          goal_type: 'savings' | 'debt_payment' | 'purchase' | 'emergency_fund' | 'investment'
          priority: 'low' | 'medium' | 'high'
          is_achieved: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          goal_name: string
          target_amount: number
          current_amount?: number
          target_date?: string | null
          goal_type: 'savings' | 'debt_payment' | 'purchase' | 'emergency_fund' | 'investment'
          priority?: 'low' | 'medium' | 'high'
          is_achieved?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          goal_name?: string
          target_amount?: number
          current_amount?: number
          target_date?: string | null
          goal_type?: 'savings' | 'debt_payment' | 'purchase' | 'emergency_fund' | 'investment'
          priority?: 'low' | 'medium' | 'high'
          is_achieved?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      monthly_summaries: {
        Row: {
          id: string
          user_id: string
          year: number
          month: number
          total_income: number
          total_expenses: number
          net_amount: number
          transactions_count: number
          largest_expense: number
          top_category_expense: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          year: number
          month: number
          total_income?: number
          total_expenses?: number
          transactions_count?: number
          largest_expense?: number
          top_category_expense?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          year?: number
          month?: number
          total_income?: number
          total_expenses?: number
          transactions_count?: number
          largest_expense?: number
          top_category_expense?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
