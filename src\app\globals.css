/*
 * Expense Tracker PY - Global Styles
 * Usando Material-UI como sistema de diseño principal
 */

/* Reset básico para compatibilidad */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: #fafafa;
  color: #333;
  min-height: 100vh;
}

/* Variables de fuentes para Material-UI */
:root {
  --font-geist-sans: 'Geist Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-geist-mono: 'Geist Mono', 'Fira Code', 'Consolas', monospace;
}

/* Estilos adicionales para mejorar la experiencia */
a {
  color: inherit;
  text-decoration: none;
}

/* Mejoras de accesibilidad */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Scrollbar personalizado para navegadores webkit */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
