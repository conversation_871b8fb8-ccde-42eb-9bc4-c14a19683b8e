import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema, ZodError } from 'zod';
import { createClient } from '@/lib/supabase/server';
import { ERROR_MESSAGES } from '@/lib/constants';

/**
 * Wrapper para manejar errores en API routes
 */
export function withErrorHandling(handler: Function) {
  return async (request: NextRequest, context?: any) => {
    try {
      return await handler(request, context);
    } catch (error) {
      console.error('API Error:', error);
      
      if (error instanceof ZodError) {
        return NextResponse.json(
          {
            error: 'Validation error',
            details: error.issues,
          },
          { status: 400 }
        );
      }
      
      if (error instanceof Error) {
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        );
      }
      
      return NextResponse.json(
        { error: ERROR_MESSAGES.GENERIC_ERROR },
        { status: 500 }
      );
    }
  };
}

/**
 * Valida el cuerpo de la request con un schema de Zod
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json();
    return schema.parse(body);
  } catch (error) {
    if (error instanceof ZodError) {
      throw error;
    }
    throw new Error('Invalid JSON body');
  }
}

/**
 * Obtiene el usuario autenticado en API routes
 */
export async function getAuthenticatedUserFromRequest(): Promise<string> {
  const supabase = await createClient();
  
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    throw new Error(ERROR_MESSAGES.UNAUTHORIZED);
  }
  
  return user.id;
}

/**
 * Verifica que el usuario tenga acceso al recurso
 */
export async function verifyResourceAccess(resourceUserId: string): Promise<void> {
  const currentUserId = await getAuthenticatedUserFromRequest();
  
  if (currentUserId !== resourceUserId) {
    throw new Error(ERROR_MESSAGES.UNAUTHORIZED);
  }
}

/**
 * Crea una respuesta de éxito estándar
 */
export function createSuccessResponse<T>(data: T, message?: string) {
  return NextResponse.json({
    success: true,
    data,
    message,
  });
}

/**
 * Crea una respuesta de error estándar
 */
export function createErrorResponse(error: string, status: number = 400) {
  return NextResponse.json(
    {
      success: false,
      error,
    },
    { status }
  );
}

/**
 * Extrae parámetros de query con validación
 */
export function getQueryParams(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  return {
    page: parseInt(searchParams.get('page') || '1'),
    limit: Math.min(parseInt(searchParams.get('limit') || '20'), 100),
    sortBy: searchParams.get('sortBy') || 'created_at',
    sortOrder: (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc',
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    type: searchParams.get('type') || '',
    startDate: searchParams.get('startDate') || '',
    endDate: searchParams.get('endDate') || '',
  };
}

/**
 * Calcula offset para paginación
 */
export function calculateOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}

/**
 * Crea respuesta paginada
 */
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
) {
  const totalPages = Math.ceil(total / limit);
  
  return NextResponse.json({
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  });
}

/**
 * Maneja métodos HTTP no permitidos
 */
export function handleMethodNotAllowed(allowedMethods: string[]) {
  return NextResponse.json(
    { error: `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}` },
    { 
      status: 405,
      headers: {
        'Allow': allowedMethods.join(', '),
      },
    }
  );
}

/**
 * Middleware para CORS
 */
export function withCors(response: NextResponse) {
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  return response;
}

/**
 * Maneja requests OPTIONS para CORS
 */
export function handleOptions() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

/**
 * Valida que los campos requeridos estén presentes
 */
export function validateRequiredFields(data: Record<string, any>, fields: string[]): void {
  const missing = fields.filter(field => !data[field]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
}

/**
 * Sanitiza datos de entrada
 */
export function sanitizeInput(data: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = value.trim();
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

/**
 * Convierte errores de Supabase a mensajes legibles
 */
export function handleSupabaseError(error: any): string {
  if (error.code === '23505') {
    return 'Este registro ya existe';
  }
  
  if (error.code === '23503') {
    return 'Referencia inválida';
  }
  
  if (error.code === 'PGRST116') {
    return 'Registro no encontrado';
  }
  
  return error.message || ERROR_MESSAGES.GENERIC_ERROR;
}

/**
 * Rate limiting simple (en memoria)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(identifier: string, maxRequests: number = 100, windowMs: number = 60000): boolean {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  const current = rateLimitMap.get(identifier);
  
  if (!current || current.resetTime < windowStart) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
}
