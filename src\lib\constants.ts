// App Configuration
export const APP_CONFIG = {
  name: process.env.NEXT_PUBLIC_APP_NAME || 'Expense Tracker PY',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  description: 'Gestor de gastos personal en guaraníes paraguayos',
  version: '1.0.0',
} as const;

// Currency Configuration
export const CURRENCY_CONFIG = {
  code: process.env.NEXT_PUBLIC_DEFAULT_CURRENCY || 'PYG',
  symbol: process.env.NEXT_PUBLIC_CURRENCY_SYMBOL || '₲',
  locale: process.env.NEXT_PUBLIC_LOCALE || 'es-PY',
  timezone: process.env.NEXT_PUBLIC_TIMEZONE || 'America/Asuncion',
} as const;

// Default Categories
export const DEFAULT_CATEGORIES = {
  expenses: [
    { name: 'Alimentación', color: '#ef4444', icon: 'utensils' },
    { name: 'Transporte', color: '#f59e0b', icon: 'car' },
    { name: 'Entretenimiento', color: '#8b5cf6', icon: 'film' },
    { name: 'Servicios', color: '#06b6d4', icon: 'zap' },
    { name: 'Salud', color: '#10b981', icon: 'heart' },
    { name: 'Educación', color: '#3b82f6', icon: 'book' },
    { name: 'Compras', color: '#f97316', icon: 'shopping-bag' },
    { name: 'Hogar', color: '#84cc16', icon: 'home' },
    { name: 'Ropa', color: '#ec4899', icon: 'shirt' },
    { name: 'Otros Gastos', color: '#6b7280', icon: 'more-horizontal' },
  ],
  income: [
    { name: 'Salario', color: '#22c55e', icon: 'dollar-sign' },
    { name: 'Freelance', color: '#84cc16', icon: 'briefcase' },
    { name: 'Inversiones', color: '#3b82f6', icon: 'trending-up' },
    { name: 'Otros Ingresos', color: '#06b6d4', icon: 'plus-circle' },
  ],
} as const;

// Transaction Types
export const TRANSACTION_TYPES = {
  INCOME: 'income',
  EXPENSE: 'expense',
  TRANSFER: 'transfer',
} as const;

// Payment Methods
export const PAYMENT_METHODS = {
  CASH: 'cash',
  CREDIT_CARD: 'credit_card',
  DEBIT_CARD: 'debit_card',
  TRANSFER: 'transfer',
  OTHER: 'other',
} as const;

// Loan Types
export const LOAN_TYPES = {
  PERSONAL: 'personal',
  MORTGAGE: 'mortgage',
  AUTO: 'auto',
  EDUCATION: 'education',
  OTHER: 'other',
} as const;

// Goal Types
export const GOAL_TYPES = {
  SAVINGS: 'savings',
  DEBT_PAYMENT: 'debt_payment',
  PURCHASE: 'purchase',
  EMERGENCY_FUND: 'emergency_fund',
  INVESTMENT: 'investment',
} as const;

// Priority Levels
export const PRIORITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
} as const;

// Budget Periods
export const BUDGET_PERIODS = {
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
} as const;

// Salary Frequencies
export const SALARY_FREQUENCIES = {
  WEEKLY: 'weekly',
  BIWEEKLY: 'biweekly',
  MONTHLY: 'monthly',
} as const;

// Recurring Frequencies
export const RECURRING_FREQUENCIES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
} as const;

// Date Formats
export const DATE_FORMATS = {
  SHORT: 'dd/MM/yyyy',
  LONG: 'dd \'de\' MMMM \'de\' yyyy',
  WITH_TIME: 'dd/MM/yyyy HH:mm',
  ISO: 'yyyy-MM-dd',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_PAGE: 1,
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  PASSWORD_MIN_LENGTH: 8,
  DESCRIPTION_MAX_LENGTH: 255,
  NAME_MAX_LENGTH: 100,
  PHONE_REGEX: /^[+]?[0-9\s\-\(\)]{8,15}$/,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  AMOUNT_MIN: 1,
  AMOUNT_MAX: 999999999999, // 999 billones de guaraníes
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  REQUIRED_FIELD: 'Este campo es requerido',
  INVALID_EMAIL: 'Email inválido',
  PASSWORD_TOO_SHORT: `La contraseña debe tener al menos ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} caracteres`,
  PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',
  INVALID_PHONE: 'Número de teléfono inválido',
  AMOUNT_TOO_LOW: 'El monto debe ser mayor a 0',
  AMOUNT_TOO_HIGH: 'El monto es demasiado alto',
  DESCRIPTION_TOO_LONG: `La descripción no puede exceder ${VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} caracteres`,
  GENERIC_ERROR: 'Ha ocurrido un error. Intenta nuevamente.',
  NETWORK_ERROR: 'Error de conexión. Verifica tu internet.',
  UNAUTHORIZED: 'No tienes permisos para realizar esta acción',
  NOT_FOUND: 'Recurso no encontrado',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  TRANSACTION_CREATED: 'Transacción creada exitosamente',
  TRANSACTION_UPDATED: 'Transacción actualizada exitosamente',
  TRANSACTION_DELETED: 'Transacción eliminada exitosamente',
  CATEGORY_CREATED: 'Categoría creada exitosamente',
  CATEGORY_UPDATED: 'Categoría actualizada exitosamente',
  CATEGORY_DELETED: 'Categoría eliminada exitosamente',
  PROFILE_UPDATED: 'Perfil actualizado exitosamente',
  PASSWORD_CHANGED: 'Contraseña cambiada exitosamente',
  EMAIL_SENT: 'Email enviado exitosamente',
  LOGIN_SUCCESS: 'Inicio de sesión exitoso',
  LOGOUT_SUCCESS: 'Sesión cerrada exitosamente',
  REGISTER_SUCCESS: 'Cuenta creada exitosamente',
} as const;

// Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  DASHBOARD: '/dashboard',
  TRANSACTIONS: '/dashboard/transactions',
  CATEGORIES: '/dashboard/categories',
  BUDGETS: '/dashboard/budgets',
  GOALS: '/dashboard/goals',
  CARDS: '/dashboard/cards',
  LOANS: '/dashboard/loans',
  PROFILE: '/dashboard/profile',
  SETTINGS: '/dashboard/settings',
  REPORTS: '/dashboard/reports',
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
  },
  TRANSACTIONS: '/api/transactions',
  CATEGORIES: '/api/categories',
  BUDGETS: '/api/budgets',
  GOALS: '/api/goals',
  CARDS: '/api/cards',
  LOANS: '/api/loans',
  PROFILE: '/api/profile',
  DASHBOARD: '/api/dashboard',
} as const;
